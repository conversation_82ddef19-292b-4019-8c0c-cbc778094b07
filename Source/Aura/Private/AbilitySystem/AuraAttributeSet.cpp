// Copyright iYuuki Studio


#include "AbilitySystem/AuraAttributeSet.h"

#include "AbilitySystemBlueprintLibrary.h"
#include "AuraAbilityTypes.h"
#include "AuraGameplayTags.h"
#include "GameplayEffectExtension.h"
#include "Aura/AuraLogChannel.h"
#include "GameFramework/Character.h"
#include "GameplayEffectComponents/TargetTagsGameplayEffectComponent.h"
#include "Interaction/CombatInterface.h"
#include "Interaction/PlayerInterface.h"
#include "Kismet/GameplayStatics.h"
#include "Net/UnrealNetwork.h"
#include "Player/AuraPlayerController.h"
#include "Player/AuraPlayerState.h"
#include "System/AuraAbilitySystemLibrary.h"
#include "UI/WidgetComponent/DamageTextComponent.h"

UAuraAttributeSet::UAuraAttributeSet()
{
	FAuraGameplayTags GameplayTags = FAuraGameplayTags::Get();

	/* By FuncPtr */

	/* Primary Attributes */

	TagsToAttributes.Add(GameplayTags.Attributes_Primary_Strength, GetStrengthAttribute);
	TagsToAttributes.Add(GameplayTags.Attributes_Primary_Intelligence, GetIntelligenceAttribute);
	TagsToAttributes.Add(GameplayTags.Attributes_Primary_Vigor, GetVigorAttribute);
	TagsToAttributes.Add(GameplayTags.Attributes_Primary_Resilience, GetResilienceAttribute);

	/* Secondary Attributes */
	TagsToAttributes.Add(GameplayTags.Attributes_Secondary_Armor, GetArmorAttribute);
	TagsToAttributes.Add(GameplayTags.Attributes_Secondary_ArmorPenetration, GetArmorPenetrationAttribute);
	TagsToAttributes.Add(GameplayTags.Attributes_Secondary_BlockChance, GetBlockChanceAttribute);
	TagsToAttributes.Add(GameplayTags.Attributes_Secondary_CriticalHitChance, GetCriticalHitChanceAttribute);
	TagsToAttributes.Add(GameplayTags.Attributes_Secondary_CriticalHitDamage, GetCriticalHitDamageAttribute);
	TagsToAttributes.Add(GameplayTags.Attributes_Secondary_CriticalResistance, GetCriticalResistanceAttribute);
	TagsToAttributes.Add(GameplayTags.Attributes_Secondary_HealthRegeneration, GetHealthRegenerationAttribute);
	TagsToAttributes.Add(GameplayTags.Attributes_Secondary_ManaRegeneration, GetManaRegenerationAttribute);
	TagsToAttributes.Add(GameplayTags.Attributes_Secondary_MaxHealth, GetMaxHealthAttribute);
	TagsToAttributes.Add(GameplayTags.Attributes_Secondary_MaxMana, GetMaxManaAttribute);

	/* Damage Resistance */
	TagsToAttributes.Add(GameplayTags.Attributes_Resistance_Fire, GetResistanceFireAttribute);
	TagsToAttributes.Add(GameplayTags.Attributes_Resistance_Lightning, GetResistanceLightningAttribute);
	TagsToAttributes.Add(GameplayTags.Attributes_Resistance_Arcane, GetResistanceArcaneAttribute);
	TagsToAttributes.Add(GameplayTags.Attributes_Resistance_Physical, GetResistancePhysicalAttribute);

	/* RetVal Delegate */

	// FGetGameplayAttribute StrengthGetter;
	// StrengthGetter.BindStatic(GetStrengthAttribute);
	// TagsToAttributes.Add(GameplayTags.Attributes_Primary_Strength, StrengthGetter);
}

void UAuraAttributeSet::GetLifetimeReplicatedProps(TArray<class FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	/* Vital Attributes */
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, Health, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, Mana, COND_None, REPNOTIFY_Always);


	/* Primary Attributes */
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, Strength, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, Intelligence, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, Vigor, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, Resilience, COND_None, REPNOTIFY_Always);


	/* Secondary Attributes */
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, MaxHealth, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, MaxMana, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, Armor, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, ArmorPenetration, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, BlockChance, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, CriticalHitChance, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, CriticalHitDamage, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, CriticalResistance, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, HealthRegeneration, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, ManaRegeneration, COND_None, REPNOTIFY_Always);

	/* Damage Resistance */
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, ResistanceFire, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, ResistanceLightning, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, ResistanceArcane, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(UAuraAttributeSet, ResistancePhysical, COND_None, REPNOTIFY_Always);
}

void UAuraAttributeSet::PreAttributeChange(const FGameplayAttribute& Attribute, float& NewValue)
{
	Super::PreAttributeChange(Attribute, NewValue);

	if (Attribute == GetHealthAttribute())
	{
		NewValue = FMath::Clamp(NewValue, 0.f, GetMaxHealth());
	}
	else if (Attribute == GetManaAttribute())
	{
		NewValue = FMath::Clamp(NewValue, 0.f, GetMaxMana());
	}
	else if (Attribute == GetMaxHealthAttribute())
	{
		NewValue = FMath::Clamp(NewValue, 0.f, 10000.f);
	}
	else if (Attribute == GetMaxManaAttribute())
	{
		NewValue = FMath::Clamp(NewValue, 0.f, 10000.f);
	}
}

void UAuraAttributeSet::PostGameplayEffectExecute(const struct FGameplayEffectModCallbackData& Data)
{
	Super::PostGameplayEffectExecute(Data);

	FEffectProperties Props;
	SetEffectProperties(Data, Props);

	if (Data.EvaluatedData.Attribute == GetHealthAttribute())
	{
		SetHealth(FMath::Clamp(GetHealth(), 0.f, GetMaxHealth()));
	}
	else if (Data.EvaluatedData.Attribute == GetManaAttribute())
	{
		SetMana(FMath::Clamp(GetMana(), 0.f, GetMaxMana()));
	}
	else if (Data.EvaluatedData.Attribute == GetIncomingDamageAttribute())
	{
		HandleIncomingDamage(Props);
	}
	else if (Data.EvaluatedData.Attribute == GetIncomingXPAttribute())
	{
		HandleIncomingXP(Props);
	}
}

void UAuraAttributeSet::PostAttributeChange(const FGameplayAttribute& Attribute, float OldValue, float NewValue)
{
	Super::PostAttributeChange(Attribute, OldValue, NewValue);
	if (Attribute == GetMaxHealthAttribute())
	{
		if (bTopOffHealth)
		{
			SetHealth(GetMaxHealth());
			bTopOffHealth = false;
		}
	}
	else if (Attribute == GetMaxManaAttribute())
	{
		if (bTopOffMana)
		{
			SetMana(GetMaxMana());
			bTopOffMana = false;
		}
	}
}

void UAuraAttributeSet::OnRep_Health(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, Health, OldValue);
}

void UAuraAttributeSet::OnRep_MaxHealth(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, MaxHealth, OldValue);
}

void UAuraAttributeSet::OnRep_Mana(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, Mana, OldValue);
}

void UAuraAttributeSet::OnRep_MaxMana(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, MaxMana, OldValue);
}

void UAuraAttributeSet::OnRep_Strength(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, Strength, OldValue);
}

void UAuraAttributeSet::OnRep_Intelligence(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, Intelligence, OldValue);
}

void UAuraAttributeSet::OnRep_Resilience(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, Resilience, OldValue);
}

void UAuraAttributeSet::OnRep_Vigor(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, Vigor, OldValue);
}

void UAuraAttributeSet::OnRep_Armor(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, Armor, OldValue);
}

void UAuraAttributeSet::OnRep_ArmorPenetration(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, ArmorPenetration, OldValue);
}

void UAuraAttributeSet::OnRep_BlockChance(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, BlockChance, OldValue);
}

void UAuraAttributeSet::OnRep_CriticalHitChance(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, CriticalHitChance, OldValue);
}

void UAuraAttributeSet::OnRep_CriticalHitDamage(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, CriticalHitDamage, OldValue);
}

void UAuraAttributeSet::OnRep_CriticalResistance(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, CriticalResistance, OldValue);
}

void UAuraAttributeSet::OnRep_HealthRegeneration(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, HealthRegeneration, OldValue);
}

void UAuraAttributeSet::OnRep_ManaRegeneration(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, ManaRegeneration, OldValue);
}

void UAuraAttributeSet::OnRep_ResistanceFire(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, ResistanceFire, OldValue);
}

void UAuraAttributeSet::OnRep_ResistanceLightning(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, ResistanceLightning, OldValue);
}

void UAuraAttributeSet::OnRep_ResistanceArcane(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, ResistanceArcane, OldValue);
}

void UAuraAttributeSet::OnRep_ResistancePhysical(const FGameplayAttributeData& OldValue) const
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(UAuraAttributeSet, ResistancePhysical, OldValue);
}

void UAuraAttributeSet::SetEffectProperties(const struct FGameplayEffectModCallbackData& Data, FEffectProperties& Props)
{
	Props.ContextHandle = Data.EffectSpec.GetContext();
	if (UAbilitySystemComponent* SourceASC = Props.ContextHandle.GetInstigatorAbilitySystemComponent())
	{
		Props.SourceASC = SourceASC;
		if (SourceASC->AbilityActorInfo.IsValid() && SourceASC->AbilityActorInfo->AvatarActor.IsValid())
		{
			Props.SourceAvatarActor = SourceASC->AbilityActorInfo->AvatarActor.Get();
			Props.SourceController = SourceASC->AbilityActorInfo->PlayerController.Get();
			if (Props.SourceController == nullptr && Props.SourceAvatarActor)
			{
				if (APawn* Pawn = Cast<APawn>(Props.SourceAvatarActor))
				{
					Props.SourceController = Pawn->GetController();
				}
			}
			if (Props.SourceController)
			{
				Props.SourceCharacter = Cast<ACharacter>(Props.SourceController->GetPawn());
			}
		}
	}

	Props.TargetAvatarActor = Data.Target.AbilityActorInfo->AvatarActor.Get();
	Props.TargetController = Data.Target.AbilityActorInfo->PlayerController.Get();
	Props.TargetCharacter = Cast<ACharacter>(Props.TargetAvatarActor);
	Props.TargetASC = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(Props.TargetAvatarActor);
}

void UAuraAttributeSet::SendXPEvent(const FEffectProperties& Props) const
{
	if (Props.TargetAvatarActor->Implements<UCombatInterface>())
	{
		int32 Level = ICombatInterface::Execute_GetCharacterLevel(Props.TargetAvatarActor);
		ECharacterClass CharacterClass = ICombatInterface::Execute_GetCharacterClass(Props.TargetAvatarActor);
		int32 XP = UAuraAbilitySystemLibrary::GetXPRewardForClassAndLevel(
			Props.TargetAvatarActor, CharacterClass, Level);

		FGameplayEventData Payload;
		Payload.EventMagnitude = XP;
		Payload.EventTag = FAuraGameplayTags::Get().Attributes_Meta_IncomingXP;
		UAbilitySystemBlueprintLibrary::SendGameplayEventToActor(Props.SourceAvatarActor,
		                                                         FAuraGameplayTags::Get().Attributes_Meta_IncomingXP,
		                                                         Payload);
	}
}

void UAuraAttributeSet::HandleIncomingDamage(FEffectProperties& Props)
{
	const float LocalIncomingDamage = GetIncomingDamage();
	SetIncomingDamage(0.f);
	if (UAuraAbilitySystemLibrary::IsSuccessfulDebuff(Props.ContextHandle))
	{
		Debuff(Props);
	}
	if (LocalIncomingDamage >= 0.f)
	{
		if (Props.TargetAvatarActor->Implements<UCombatInterface>())
		{
			if (bool bIsDead = ICombatInterface::Execute_IsDead(Props.TargetAvatarActor))
				return;
		}

		const float NewValue = GetHealth() - LocalIncomingDamage;
		SetHealth(FMath::Clamp(NewValue, 0.f, GetMaxHealth()));

		if (NewValue <= 0) // Damage is Fatal
		{
			ICombatInterface* CombatInterface = Cast<ICombatInterface>(Props.TargetAvatarActor);
			if (CombatInterface != nullptr)
			{
				const FVector DeathImpulse = UAuraAbilitySystemLibrary::GetDeathImpulse(Props.ContextHandle);
				CombatInterface->Die(DeathImpulse);
				SendXPEvent(Props);
			}
		}
		else // Damage is not fatal
		{
			if (Props.TargetCharacter)
			{
				// HitReact or Shocked
				bool IsShocking = Props.TargetASC->HasMatchingGameplayTag(FAuraGameplayTags::Get().Effects_Shocked);
				if (IsShocking)
				{
				}
				else
				{
					FGameplayTagContainer Tags;
					Tags.AddTag(FAuraGameplayTags::Get().Effects_HitReact);
					Props.TargetASC->TryActivateAbilitiesByTag(Tags);
				}


				// Knockback
				if (UAuraAbilitySystemLibrary::IsSuccessfulKnockback(Props.ContextHandle))
				{
					FVector Knockback = UAuraAbilitySystemLibrary::GetKnockback(Props.ContextHandle);
					Props.TargetCharacter->LaunchCharacter(Knockback,
					                                       true, true);
				}
			}
		}
		ShowDamageText(Props, LocalIncomingDamage);
	}
}

void UAuraAttributeSet::ShowDamageText(const FEffectProperties& Props, float LocalIncomingDamage)
{
	if (Props.TargetAvatarActor)
	{
		APlayerController* PlayerController = Cast<APlayerController>(Props.SourceController);
		if (AAuraPlayerController* AuraPlayerController = Cast<AAuraPlayerController>(PlayerController))
		{
			AuraPlayerController->ShowDamageFloatText(LocalIncomingDamage, Props.TargetAvatarActor,
			                                          UAuraAbilitySystemLibrary::IsBlockedHit(
				                                          Props.ContextHandle),
			                                          UAuraAbilitySystemLibrary::IsCriticalHit(
				                                          Props.ContextHandle));
		}
		else if (AAuraPlayerController* AuraPC = Cast<AAuraPlayerController>(Props.TargetController))
		{
			AuraPC->ShowDamageFloatText(LocalIncomingDamage, Props.TargetAvatarActor,
			                            UAuraAbilitySystemLibrary::IsBlockedHit(Props.ContextHandle),
			                            UAuraAbilitySystemLibrary::IsCriticalHit(Props.ContextHandle));
		}
	}
}


void UAuraAttributeSet::HandleIncomingXP(const FEffectProperties& Props)
{
	const float LocalIncomingXP = GetIncomingXP();
	SetIncomingXP(0.f);
	if (LocalIncomingXP >= 0.f) // Get XP
	{
		AActor* SourceAvatarActor = Props.SourceAvatarActor;
		if (SourceAvatarActor->Implements<UPlayerInterface>() && SourceAvatarActor->Implements<UCombatInterface>())
		{
			int32 OldLevel = ICombatInterface::Execute_GetCharacterLevel(SourceAvatarActor);

			IPlayerInterface::Execute_AddToXp(SourceAvatarActor, LocalIncomingXP);

			int32 NewXP = IPlayerInterface::Execute_GetXP(SourceAvatarActor);
			int32 NewLevel = IPlayerInterface::Execute_GetLevelForXP(SourceAvatarActor, NewXP);
			if (NewLevel > OldLevel) // Level Ups
			{
				int32 AttributePoints = IPlayerInterface::Execute_GetAttributePointsRewardBetweenLevels(
					SourceAvatarActor, OldLevel, NewLevel);
				int32 SpellPoints = IPlayerInterface::Execute_GetSpellPointsRewardBetweenLevels(
					SourceAvatarActor, OldLevel, NewLevel);
				IPlayerInterface::Execute_AddToAttributePoints(SourceAvatarActor, AttributePoints);
				IPlayerInterface::Execute_AddToSpellPoints(SourceAvatarActor, SpellPoints);

				IPlayerInterface::Execute_LevelUp(SourceAvatarActor);
				IPlayerInterface::Execute_AddToLevel(SourceAvatarActor, NewLevel - OldLevel);

				// Reset Health and Mana
				bTopOffHealth = true;
				bTopOffMana = true;
			}
		}
	}
}

void UAuraAttributeSet::Debuff(const FEffectProperties& Props)
{
	FGameplayEffectContextHandle ContextHandle = Props.SourceASC->MakeEffectContext();
	ContextHandle.AddSourceObject(Props.SourceAvatarActor);

	const FGameplayTag DamageType = UAuraAbilitySystemLibrary::GetDamageType(Props.ContextHandle);
	const float DebuffDamage = UAuraAbilitySystemLibrary::GetDebuffDamage(Props.ContextHandle);
	const float DebuffDuration = UAuraAbilitySystemLibrary::GetDebuffDuration(Props.ContextHandle);
	const float DebuffFrequency = UAuraAbilitySystemLibrary::GetDebuffFrequency(Props.ContextHandle);

	FString DebuffName = FString::Printf(
		TEXT("Debuff_%s"), *FAuraGameplayTags::Get().DamageTypesToDebuffs[DamageType].GetTagName().ToString());
	UGameplayEffect* DebuffEffect = NewObject<UGameplayEffect>(GetTransientPackage(), FName(DebuffName));

	DebuffEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
	DebuffEffect->Period = DebuffFrequency;
	DebuffEffect->DurationMagnitude = FScalableFloat(DebuffDuration);

	DebuffEffect->StackingType = EGameplayEffectStackingType::AggregateBySource;
	DebuffEffect->StackLimitCount = 1;

	// InheritableOwnedTagsContainer已被弃用
	// DebuffEffect->InheritableOwnedTagsContainer.AddTag(FAuraGameplayTags::Get().DamageTypesToDebuffs[DamageType]);
	FInheritedTagContainer TagContainer;
	UTargetTagsGameplayEffectComponent& Component = DebuffEffect->FindOrAddComponent<
		UTargetTagsGameplayEffectComponent>();

	FGameplayTag DebuffTag = FAuraGameplayTags::Get().DamageTypesToDebuffs[DamageType];
	TagContainer.AddTag(DebuffTag);

	Component.SetAndApplyTargetTagChanges(TagContainer);


	FGameplayModifierInfo ModifierInfo;
	ModifierInfo.Attribute = GetIncomingDamageAttribute();
	ModifierInfo.ModifierOp = EGameplayModOp::Additive;
	ModifierInfo.ModifierMagnitude = FScalableFloat(DebuffDamage);
	DebuffEffect->Modifiers.Add(ModifierInfo);

	FGameplayEffectSpec EffectSpec(DebuffEffect, ContextHandle, 1.f);
	Props.TargetASC->ApplyGameplayEffectSpecToSelf(EffectSpec);
}
