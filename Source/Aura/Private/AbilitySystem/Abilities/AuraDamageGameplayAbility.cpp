// Copyright iYuuki Studio


#include "AbilitySystem/Abilities/AuraDamageGameplayAbility.h"

#include "AbilitySystemBlueprintLibrary.h"
#include "AbilitySystemComponent.h"
#include "System/AuraAbilitySystemLibrary.h"

void UAuraDamageGameplayAbility::CauseDamage(AActor* TargetActor)
{
	if (!HasAuthority(&CurrentActivationInfo))return;
	const FDamageEffectParams Params = MakeDamageEffectParamsFromClassDefaults(TargetActor);
	UAuraAbilitySystemLibrary::ApplyDamageEffect(Params);
}

void UAuraDamageGameplayAbility::CauseDamageWithoutDebuff(AActor* TargetActor)
{
	FDamageEffectParams Params = MakeDamageEffectParamsFromClassDefaults(TargetActor);
	Params.DebuffChance = 0.f;
	UAuraAbilitySystemLibrary::ApplyDamageEffect(Params);
}

void UAuraDamageGameplayAbility::CauseDebuff(AActor* TargetActor)
{
	FDamageEffectParams Params = MakeDamageEffectParamsFromClassDefaults(TargetActor);
	Params.BaseDamage = 0.f;
	UAuraAbilitySystemLibrary::ApplyDamageEffect(Params);
}

FDamageEffectParams UAuraDamageGameplayAbility::MakeDamageEffectParamsFromClassDefaults(AActor* TargetActor) const
{
	FDamageEffectParams Params;
	Params.WorldContextObject = GetAvatarActorFromActorInfo();
	Params.DamageGameplayEffectClass = DamageEffectClass;
	Params.TargetAbilitySystemComponent = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(TargetActor);
	Params.SourceAbilitySystemComponent = GetAbilitySystemComponentFromActorInfo();
	Params.BaseDamage = Damage.GetValueAtLevel(GetAbilityLevel());
	Params.DamageType = DamageType;
	Params.AbilityLevel = GetAbilityLevel();
	Params.DeathImpulseMagnitude = DeathImpulseMagnitude;
	Params.KnockbackMagnitude = KnockbackMagnitude;
	Params.KnockbackChance = KnockbackChance;
	Params.DebuffDamage = DebuffDamage;
	Params.DebuffDuration = DebuffDuration;
	Params.DebuffFrequency = DebuffFrequency;
	Params.DebuffChance = DebuffChance;

	if (TargetActor)
	{
		const FVector ToTartet = TargetActor->GetActorLocation() - GetAvatarActorFromActorInfo()->GetActorLocation();
		const FRotator ToTargetRotation = ToTartet.Rotation();
		Params.Knockback = ToTargetRotation.Vector() * KnockbackMagnitude;
		Params.DeathImpulse = ToTargetRotation.Vector() * DeathImpulseMagnitude;
	}
	return Params;
}
