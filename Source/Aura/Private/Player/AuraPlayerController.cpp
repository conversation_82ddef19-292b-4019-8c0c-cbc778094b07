// Copyright iYuuki Studio


#include "Player/AuraPlayerController.h"

#include "AbilitySystemBlueprintLibrary.h"
#include "AuraGameplayTags.h"
#include "EnhancedInputSubsystems.h"
#include "EnhancedInputComponent.h"
#include "NavigationPath.h"
#include "NavigationSystem.h"
#include "NiagaraFunctionLibrary.h"
#include "AbilitySystem/AuraAbilitySystemComponent.h"
#include "Components/SplineComponent.h"
#include "Input/AuraInputComponent.h"
#include "Interaction/EnemyInterface.h"
#include "System/AuraAbilitySystemLibrary.h"
#include "UI/WidgetComponent/DamageTextComponent.h"

AAuraPlayerController::AAuraPlayerController()
{
	bReplicates = true;

	SplineComponent = CreateDefaultSubobject<USplineComponent>("SplineComponent");
}


void AAuraPlayerController::ShowDamageFloatText_Implementation(const float Value, AActor* TargetActor, bool bBlockedHit,
                                                               bool bCriticalHit)
{
	if (IsValid(TargetActor) && DamageTextComponentClass)
	{
		UDamageTextComponent* DamageTextComponent = NewObject<UDamageTextComponent>(
			TargetActor, DamageTextComponentClass);
		DamageTextComponent->RegisterComponent();
		DamageTextComponent->AttachToComponent(TargetActor->GetRootComponent(),
		                                       FAttachmentTransformRules::KeepRelativeTransform);
		DamageTextComponent->DetachFromComponent(FDetachmentTransformRules::KeepWorldTransform);
		DamageTextComponent->SetDamageText(Value, bBlockedHit, bCriticalHit);
	}
}

void AAuraPlayerController::BeginPlay()
{
	Super::BeginPlay();
	check(AuraContext);

	UEnhancedInputLocalPlayerSubsystem* Subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(
		GetLocalPlayer());
	if (!Subsystem) return;
	Subsystem->AddMappingContext(AuraContext, 0);

	bShowMouseCursor = true;
	DefaultMouseCursor = EMouseCursor::Default;

	FInputModeGameAndUI InputModeData;
	InputModeData.SetLockMouseToViewportBehavior(EMouseLockMode::DoNotLock);
	InputModeData.SetHideCursorDuringCapture(false);
	SetInputMode(InputModeData);
}

void AAuraPlayerController::Tick(float DeltaSeconds)
{
	Super::Tick(DeltaSeconds);

	TraceCursor();
	AutoRun();
}

void AAuraPlayerController::SetupInputComponent()
{
	Super::SetupInputComponent();

	UAuraInputComponent* EnhancedInputComponent = CastChecked<UAuraInputComponent>(InputComponent);
	EnhancedInputComponent->BindAction(MoveAction, ETriggerEvent::Triggered, this, &AAuraPlayerController::Move);
	EnhancedInputComponent->BindAction(ShiftAction, ETriggerEvent::Started, this, &AAuraPlayerController::ShiftPressed);
	EnhancedInputComponent->BindAction(ShiftAction, ETriggerEvent::Completed, this,
	                                   &AAuraPlayerController::ShiftReleased);

	EnhancedInputComponent->BindAbilityActions(InputConfig, this, &ThisClass::AbilityInputTagPressed,
	                                           &ThisClass::AbilityInputTagReleased, &ThisClass::AbilityInputTagHeld);
}

void AAuraPlayerController::Move(const FInputActionValue& InputActionValue)
{
	if (GetAuraASC() && GetAuraASC()->HasMatchingGameplayTag(FAuraGameplayTags::Get().Player_Block_Movement)) return;
	
	FVector2D InputVector = InputActionValue.Get<FVector2D>();

	FRotator Rotation = GetControlRotation();
	Rotation.Pitch = 0;
	Rotation.Roll = 0;

	FVector ForwardVector = FRotationMatrix(Rotation).GetUnitAxis(EAxis::X);
	FVector RightVector = FRotationMatrix(Rotation).GetUnitAxis(EAxis::Y);

	APawn* MyPawn = GetPawn();
	if (MyPawn == nullptr) return;
	bAutoRunning = false;
	MyPawn->AddMovementInput(ForwardVector, InputVector.Y);
	MyPawn->AddMovementInput(RightVector, InputVector.X);
}

void AAuraPlayerController::TraceCursor()
{
	if (GetAuraASC() && GetAuraASC()->HasMatchingGameplayTag(FAuraGameplayTags::Get().Player_Block_CursorTrace))
	{
		if (LastCursorTraceActor) LastCursorTraceActor->UnHighlight();
		if (CurrentCursorTraceActor) CurrentCursorTraceActor->UnHighlight();
		CurrentCursorTraceActor = nullptr;
		LastCursorTraceActor = nullptr;
		return;
	}
	
	GetHitResultUnderCursor(ECC_Visibility, false, CursorHitResult);
	if (!CursorHitResult.bBlockingHit) return;

	auto HitActor = CursorHitResult.GetActor();
	if (!HitActor) return;
	if (HitActor->GetClass()->ImplementsInterface(UEnemyInterface::StaticClass()))
	{
		// 构造 TScriptInterface<IEnemyInterface>
		TScriptInterface<IEnemyInterface> EnemyInterface;
		EnemyInterface.SetObject(HitActor);
		EnemyInterface.SetInterface(Cast<IEnemyInterface>(HitActor));

		CurrentCursorTraceActor = EnemyInterface;
	}
	else
	{
		CurrentCursorTraceActor = nullptr;
	}


	if (LastCursorTraceActor != CurrentCursorTraceActor)
	{
		if (LastCursorTraceActor) LastCursorTraceActor->UnHighlight();
		if (CurrentCursorTraceActor) CurrentCursorTraceActor->HighLight();
		LastCursorTraceActor = CurrentCursorTraceActor;
	}
}

void AAuraPlayerController::AutoRun()
{
	if (bAutoRunning)
	{
		APawn* MyPawn = GetPawn();
		if (MyPawn == nullptr) return;

		const FVector LocationOnSpline = SplineComponent->FindLocationClosestToWorldLocation(
			MyPawn->GetActorLocation(), ESplineCoordinateSpace::World);
		const FVector FDirection = SplineComponent->FindDirectionClosestToWorldLocation(
			LocationOnSpline, ESplineCoordinateSpace::World);
		MyPawn->AddMovementInput(FDirection, 1.f);

		float DistanceToDestination = (LocationOnSpline - CachedDestination).Size2D();
		if (DistanceToDestination <= AutoRunAcceptanceRadius)
		{
			bAutoRunning = false;
		}
	}
}

void AAuraPlayerController::AbilityInputTagPressed(FGameplayTag InputTag)
{
	if (GetAuraASC() && GetAuraASC()->HasMatchingGameplayTag(FAuraGameplayTags::Get().Player_Block_InputPressed)) return;
	
	if (CurrentCursorTraceActor) bTargeting = true;
	else
		bTargeting = false;

	bAutoRunning = false;
	FollowTime = 0.f;
}

void AAuraPlayerController::AbilityInputTagHeld(FGameplayTag InputTag)
{
	if (GetAuraASC() && GetAuraASC()->HasMatchingGameplayTag(FAuraGameplayTags::Get().Player_Block_InputHeld)) return;
	
	if (InputTag != FAuraGameplayTags::Get().InputTag_LMB)
	{
		if (GetAuraASC())
			GetAuraASC()->AbilityInputTagHeld(InputTag);
	}
	else
	{
		if (bTargeting || bShiftPressed)
		{
			if (GetAuraASC())
				GetAuraASC()->AbilityInputTagHeld(InputTag);
		}
		else
		{
			FollowTime += GetWorld()->GetDeltaSeconds();

			APawn* MyPawn = GetPawn();
			if (MyPawn == nullptr) return;

			if (CursorHitResult.bBlockingHit)
				CachedDestination = CursorHitResult.ImpactPoint;
			FVector WorldDirection = (CachedDestination - GetPawn()->GetActorLocation()).GetSafeNormal();
			MyPawn->AddMovementInput(WorldDirection, 1.f);
		}
	}
}

void AAuraPlayerController::AbilityInputTagReleased(FGameplayTag InputTag)
{
	if (GetAuraASC() && GetAuraASC()->HasMatchingGameplayTag(FAuraGameplayTags::Get().Player_Block_InputReleased)) return;
		
	if (InputTag != FAuraGameplayTags::Get().InputTag_LMB)
	{
		if (GetAuraASC())
			GetAuraASC()->AbilityInputTagReleased(InputTag);
	}
	else
	{
		if (bTargeting || bShiftPressed)
		{
			if (GetAuraASC())
				GetAuraASC()->AbilityInputTagReleased(InputTag);
		}
		else
		{
			if (FollowTime <= ShortPressThreshold)
			{
				// Short press(click to move)
				if (APawn* MyPawn = GetPawn())
				{
					if (UNavigationPath* NavPath = UNavigationSystemV1::FindPathToLocationSynchronously(
						this, MyPawn->GetActorLocation(), CachedDestination))
					{
						SplineComponent->ClearSplinePoints();
						for (const FVector& PathPoint : NavPath->PathPoints)
						{
							SplineComponent->AddSplineWorldPoint(PathPoint);
						}
						if (NavPath->PathPoints.Num() > 0)
							CachedDestination = NavPath->PathPoints.Last();
					}
					bAutoRunning = true;

					if (CursorHitResult.bBlockingHit)
					{
						UNiagaraFunctionLibrary::SpawnSystemAtLocation(
							this, MovementNiagaraSystem, CursorHitResult.ImpactPoint);
					}
				}
			}
			FollowTime = 0.f;
		}
	}
}


UAuraAbilitySystemComponent* AAuraPlayerController::GetAuraASC()
{
	if (AuraASC == nullptr)
	{
		AuraASC = Cast<UAuraAbilitySystemComponent>(
			UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(GetPawn()));
	}
	return AuraASC;
}
