// Copyright iYuuki Studio


#include "Character/AuraCharacter.h"

#include "AbilitySystemComponent.h"
#include "AuraGameplayTags.h"
#include "NiagaraComponent.h"
#include "AbilitySystem/AuraAbilitySystemComponent.h"
#include "AbilitySystem/Data/LevelUpInfo.h"
#include "Camera/CameraComponent.h"
#include "Components/WidgetComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "GameFramework/SpringArmComponent.h"
#include "Kismet/GameplayStatics.h"
#include "Player/AuraPlayerController.h"
#include "Player/AuraPlayerState.h"
#include "UI/HUD/AuraHUD.h"

AAuraCharacter::AAuraCharacter()
{
	LevelUpNiagaraComponent = CreateDefaultSubobject<UNiagaraComponent>("LevelUpNiagaraComponent");
	LevelUpNiagaraComponent->SetupAttachment(GetRootComponent());
	LevelUpNiagaraComponent->SetAutoActivate(false);

	CameraBoom = CreateDefaultSubobject<USpringArmComponent>("CameraBoom");
	CameraBoom->SetupAttachment(GetRootComponent());
	CameraComponent = CreateDefaultSubobject<UCameraComponent>("PlayerCamera");
	CameraComponent->SetupAttachment(CameraBoom, USpringArmComponent::SocketName);


	if (auto MovementComponent = GetCharacterMovement())
	{
		MovementComponent->bOrientRotationToMovement = true;
		MovementComponent->RotationRate = FRotator(0.f, 400.f, 0.f);
		MovementComponent->bConstrainToPlane = true;
		MovementComponent->bSnapToPlaneAtStart = true;

		bUseControllerRotationPitch = false;
		bUseControllerRotationRoll = false;
		bUseControllerRotationYaw = false;
	}

	CharacterClass = ECharacterClass::Elementalist;
}

void AAuraCharacter::AddToXp_Implementation(int32 XP)
{
	AAuraPlayerState* AuraPlayerState = Cast<AAuraPlayerState>(GetPlayerState());
	check(AuraPlayerState);
	AuraPlayerState->AddToXP(XP);
}

void AAuraCharacter::AddToLevel_Implementation(int32 InLevel)
{
	AAuraPlayerState* AuraPlayerState = Cast<AAuraPlayerState>(GetPlayerState());
	check(AuraPlayerState);
	AuraPlayerState->AddToLevel(InLevel);

	UAuraAbilitySystemComponent* AuraASC = Cast<UAuraAbilitySystemComponent>(AbilitySystemComponent);
	check(AuraASC);
	AuraASC->UpdateAbilityStatusByLevel(AuraPlayerState->GetPlayerLevel());
}

void AAuraCharacter::LevelUp_Implementation()
{
	MulticastLevelUpParticles();

	if (LevelUpWidgetClass)
	{
		APlayerController* PC = GetLocalViewingPlayerController();
		FVector WorldLocation = GetActorLocation() + FVector(0, 0, 80); // 角色头顶
		FVector2D ScreenPosition;
		bool bProjected = UGameplayStatics::ProjectWorldToScreen(PC, WorldLocation, ScreenPosition);
		if (bProjected)
		{
			UUserWidget* LevelUpWidget = CreateWidget<UUserWidget>(PC, LevelUpWidgetClass);
			if (LevelUpWidget)
			{
				LevelUpWidget->AddToViewport();

				LevelUpWidget->SetPositionInViewport(ScreenPosition, true);
			}
		}
	}
}

int32 AAuraCharacter::GetXP_Implementation()
{
	AAuraPlayerState* AuraPlayerState = Cast<AAuraPlayerState>(GetPlayerState());
	check(AuraPlayerState);
	return AuraPlayerState->GetXP();
}

int32 AAuraCharacter::GetLevelForXP_Implementation(int32 InXP)
{
	AAuraPlayerState* AuraPlayerState = Cast<AAuraPlayerState>(GetPlayerState());
	check(AuraPlayerState);
	return AuraPlayerState->LevelUpInformation->FindLevelForXP(InXP);
}

int32 AAuraCharacter::GetAttributePointsRewardBetweenLevels_Implementation(int32 InOldLevel, int32 InNewLevel)
{
	AAuraPlayerState* AuraPlayerState = Cast<AAuraPlayerState>(GetPlayerState());
	check(AuraPlayerState);
	int32 AttributePoints = 0;
	for (int32 L = InOldLevel; L < InNewLevel; ++L)
	{
		AttributePoints += AuraPlayerState->LevelUpInformation->LevelUpInfos[L].AttributePointReward;
	}
	return AttributePoints;
}

int32 AAuraCharacter::GetSpellPointsRewardBetweenLevels_Implementation(int32 InOldLevel, int32 InNewLevel)
{
	AAuraPlayerState* AuraPlayerState = Cast<AAuraPlayerState>(GetPlayerState());
	check(AuraPlayerState);
	int32 SpellPoints = 0;
	for (int32 L = InOldLevel; L < InNewLevel; ++L)
	{
		SpellPoints += AuraPlayerState->LevelUpInformation->LevelUpInfos[L].SpellPointReward;
	}
	return SpellPoints;
}

void AAuraCharacter::AddToAttributePoints_Implementation(int32 InAttributePoints)
{
	AAuraPlayerState* AuraPlayerState = Cast<AAuraPlayerState>(GetPlayerState());
	check(AuraPlayerState);
	AuraPlayerState->AddToAttributePoints(InAttributePoints);
}

void AAuraCharacter::AddToSpellPoints_Implementation(int32 InSpellPoints)
{
	AAuraPlayerState* AuraPlayerState = Cast<AAuraPlayerState>(GetPlayerState());
	check(AuraPlayerState);
	AuraPlayerState->AddToSpellPoints(InSpellPoints);
}

void AAuraCharacter::BeginPlay()
{
	Super::BeginPlay();
}

void AAuraCharacter::PossessedBy(AController* NewController)
{
	Super::PossessedBy(NewController);

	InitAbilityActorInfo();
	AddCharacterAbilities();
}

void AAuraCharacter::OnRep_PlayerState()
{
	Super::OnRep_PlayerState();

	InitAbilityActorInfo();
}

void AAuraCharacter::AddCharacterAbilities()
{
	Super::AddCharacterAbilities();
}

int32 AAuraCharacter::GetCharacterLevel_Implementation()
{
	if (const AAuraPlayerState* AuraPlayerState = GetPlayerStateChecked<AAuraPlayerState>())
	{
		return AuraPlayerState->GetPlayerLevel();
	}
	return 1;
}

void AAuraCharacter::InitAbilityActorInfo()
{
	AAuraPlayerState* AuraPlayerState = Cast<AAuraPlayerState>(GetPlayerState());
	check(AuraPlayerState);

	AuraPlayerState->GetAbilitySystemComponent()->InitAbilityActorInfo(AuraPlayerState, this);
	AbilitySystemComponent = AuraPlayerState->GetAbilitySystemComponent();

	UAuraAbilitySystemComponent* AuraASC = Cast<UAuraAbilitySystemComponent>(AbilitySystemComponent);
	check(AuraASC);
	AuraASC->AbilityActorInfoSet();

	AttributeSet = AuraPlayerState->GetAttributeSet();

	if (AAuraPlayerController* AuraPlayerController = Cast<AAuraPlayerController>(GetController()))
	{
		auto HUD = AuraPlayerController->GetHUD();
		AAuraHUD* AuraHUD = Cast<AAuraHUD>(HUD);
		if (AuraHUD)
		{
			AuraHUD->InitOverlay(AuraPlayerController, AuraPlayerState, AbilitySystemComponent, AttributeSet);
		}
	}

	InitialAttributes();
	OnRegisteredASC.Broadcast(AbilitySystemComponent);

	// Stun status change
	AuraASC->RegisterGameplayTagEvent(FAuraGameplayTags::Get().Debuff_Stun, EGameplayTagEventType::NewOrRemoved)
	       .AddLambda([this](const FGameplayTag Tag, int32 NewCount)
	       {
		       bStunned = NewCount > 0;
		       OnStunned();
	       });

	// Shock Status change
	AuraASC->RegisterGameplayTagEvent(FAuraGameplayTags::Get().Effects_Shocked, EGameplayTagEventType::NewOrRemoved)
	       .AddLambda([this](const FGameplayTag Tag, int32 NewCount)
	       {
		       bShocked = NewCount > 0;
	       });
}

void AAuraCharacter::OnStunned()
{
	FGameplayTagContainer BlockedTags;
	BlockedTags.AddTag(FAuraGameplayTags::Get().Player_Block_CursorTrace);
	BlockedTags.AddTag(FAuraGameplayTags::Get().Player_Block_InputHeld);
	BlockedTags.AddTag(FAuraGameplayTags::Get().Player_Block_InputPressed);
	BlockedTags.AddTag(FAuraGameplayTags::Get().Player_Block_InputReleased);
	BlockedTags.AddTag(FAuraGameplayTags::Get().Player_Block_Movement);

	if (bStunned)
	{
		AbilitySystemComponent->AddLooseGameplayTags(BlockedTags);
	}
	else
	{
		AbilitySystemComponent->RemoveLooseGameplayTags(BlockedTags);
	}
}

void AAuraCharacter::OnRep_Stunned()
{
	OnStunned();
}

void AAuraCharacter::MulticastLevelUpParticles_Implementation()
{
	if (LevelUpNiagaraComponent)
	{
		FVector CameraLocation = CameraComponent->GetComponentLocation();
		FVector NiagaraLocation = LevelUpNiagaraComponent->GetComponentLocation();
		FRotator ToCameraRotation = (CameraLocation - NiagaraLocation).Rotation();
		LevelUpNiagaraComponent->SetWorldRotation(ToCameraRotation);

		LevelUpNiagaraComponent->Activate(true);
	}

	if (LevelUpSound)
	{
		UGameplayStatics::PlaySoundAtLocation(this, LevelUpSound, GetActorLocation());
	}
}
