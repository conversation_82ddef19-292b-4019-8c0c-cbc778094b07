// Copyright iYuuki Studio


#include "Character/Enemy.h"

#include "AuraGameplayTags.h"
#include "Character/AuraCharacter.h"
#include "AbilitySystem/AuraAbilitySystemComponent.h"
#include "AbilitySystem/AuraAttributeSet.h"
#include "AI/AuraAIController.h"
#include "BehaviorTree/BehaviorTree.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "Components/WidgetComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "System/AuraAbilitySystemLibrary.h"
#include "UI/Widget/AuraUserWidget.h"
#include "UI/WidgetController/AuraWidgetController.h"


AEnemy::AEnemy()
{
	GetMesh()->SetCollisionResponseToChannel(ECC_Visibility, ECR_Block);

	AbilitySystemComponent = CreateDefaultSubobject<UAuraAbilitySystemComponent>("AbilitySystemComponent");
	AbilitySystemComponent->SetIsReplicated(true);
	AbilitySystemComponent->SetReplicationMode(EGameplayEffectReplicationMode::Minimal);

	AttributeSet = CreateDefaultSubobject<UAuraAttributeSet>("AttributeSet");

	AutoPossessAI = EAutoPossessAI::PlacedInWorldOrSpawned;
}

void AEnemy::PossessedBy(AController* NewController)
{
	Super::PossessedBy(NewController);

	AuraAIController = Cast<AAuraAIController>(NewController);
	AuraAIController->GetBlackboardComponent()->InitializeBlackboard(*BehaviorTree->BlackboardAsset);
	AuraAIController->RunBehaviorTree(BehaviorTree);

	AuraAIController->GetBlackboardComponent()->SetValueAsBool(FName("RangedAttacker"),
	                                                           CharacterClass != ECharacterClass::Warrior);
}

void AEnemy::HighLight()
{
	GetMesh()->SetRenderCustomDepth(true);
	GetMesh()->SetCustomDepthStencilValue(250);
	Weapon->SetRenderCustomDepth(true);
	Weapon->SetCustomDepthStencilValue(250);
}

void AEnemy::UnHighlight()
{
	GetMesh()->SetRenderCustomDepth(false);
	GetMesh()->SetCustomDepthStencilValue(0);
	Weapon->SetRenderCustomDepth(false);
	Weapon->SetCustomDepthStencilValue(0);
}

int32 AEnemy::GetCharacterLevel_Implementation()
{
	return Level;
}

void AEnemy::Die(const FVector& DeathImpulse)
{
	SetLifeSpan(LifeSpan);
	Super::Die(DeathImpulse);
	Dissolve();

	if (AuraAIController)
		AuraAIController->GetBlackboardComponent()->SetValueAsBool(FName("Dead"),
		                                                           true);
}

void AEnemy::HitReactTagChanged(const FGameplayTag Tag, int32 NewCount)
{
	bHitReacting = NewCount > 0;
	GetCharacterMovement()->MaxWalkSpeed = bHitReacting ? 0.f : BaseWalkSpeed;
	if (HasAuthority())
		AuraAIController->GetBlackboardComponent()->SetValueAsBool(FName("HitReacting"), bHitReacting);
}

void AEnemy::BeginPlay()
{
	Super::BeginPlay();

	/* Initialize Ability System */
	InitAbilityActorInfo();
	if (HasAuthority())
	{
		InitialAttributes();
		UAuraAbilitySystemLibrary::GiveStartupAbilities(this, AbilitySystemComponent, CharacterClass);
	}

	/* Initialize Startup info */
	GetCharacterMovement()->MaxWalkSpeed = BaseWalkSpeed;
	OnRegisteredASC.Broadcast(AbilitySystemComponent);
}

void AEnemy::InitAbilityActorInfo()
{
	AbilitySystemComponent->InitAbilityActorInfo(this, this);
	UAuraAbilitySystemComponent* AuraASC = Cast<UAuraAbilitySystemComponent>(AbilitySystemComponent);
	if (AuraASC)
		AuraASC->AbilityActorInfoSet();


	FAuraGameplayTags AuraGameplayTags = FAuraGameplayTags::Get();
	AbilitySystemComponent->RegisterGameplayTagEvent(AuraGameplayTags.Effects_HitReact,
	                                                 EGameplayTagEventType::NewOrRemoved).AddUObject(
		this, &AEnemy::HitReactTagChanged);

	if (HasAuthority())
	{
		// Stun status change
		AbilitySystemComponent->RegisterGameplayTagEvent(FAuraGameplayTags::Get().Debuff_Stun,
		                                                 EGameplayTagEventType::NewOrRemoved).
		                        AddLambda(
			                        [this](const FGameplayTag Tag, int32 NewCount)
			                        {
				                        bStunned = NewCount > 0;
				                        AuraAIController->GetBlackboardComponent()->SetValueAsBool(
					                        FName("Stunned"), bStunned);
			                        }
		                        );

		// Shock Status change
		AbilitySystemComponent->RegisterGameplayTagEvent(FAuraGameplayTags::Get().Effects_Shocked,
		                                                 EGameplayTagEventType::NewOrRemoved)
		                      .AddLambda([this](const FGameplayTag Tag, int32 NewCount)
		                      {
			                      bShocked = NewCount > 0;
		                      });
	}
}

void AEnemy::InitialAttributes()
{
	UAuraAbilitySystemLibrary::InitializeDefaultAttributes(this, CharacterClass, Level, AbilitySystemComponent);
}
