// Copyright iYuuki Studio

#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h"
#include "UObject/Interface.h"
#include "CombatInterface.generated.h"

class UAbilitySystemComponent;
DECLARE_MULTICAST_DELEGATE_OneParam(FOnRegisteredASCDelegate, UAbilitySystemComponent*);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnDieDelegate);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnDeathDelegate, AActor*, DeathTarget);

enum class ECharacterClass : uint8;
class UNiagaraSystem;

USTRUCT(BlueprintType)
struct FTaggedMontage
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Montage")
	TArray<TObjectPtr<UAnimMontage>> Montages;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Montage")
	FGameplayTag MontageTag;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Montage")
	FGameplayTag CombatSocketTag;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Montage")
	TObjectPtr<USoundBase> ImpactSound;
};

// This class does not need to be modified.
UINTERFACE(MinimalAPI, BlueprintType)
class UCombatInterface : public UInterface
{
	GENERATED_BODY()
};

/**
 * 
 */
class AURA_API ICombatInterface
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Combat")
	int32 GetCharacterLevel();

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Combat")
	FVector GetCombatSocketLocation(FGameplayTag AttackTag);

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Combat")
	void UpdateFacingTarget(const FVector& Target);

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Combat")
	UNiagaraSystem* GetBloodEffect();

	UFUNCTION(Category = "Combat")
	virtual void Die(const FVector& DeathImpulse) = 0;

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Combat")
	bool IsDead();

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Combat")
	ECharacterClass GetCharacterClass();

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Combat")
	void SetInShockSpelling(bool bInShockSpelling);

	virtual FOnRegisteredASCDelegate GetOnRegisteredASCDelegate() = 0;
	UFUNCTION()
	virtual FOnDieDelegate GetOnDieDelegate() = 0;
	virtual FOnDeathDelegate& GetOndeathDelegate() = 0;

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Montage")
	TArray<FTaggedMontage> GetAttackMontages();

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Montage")
	FTaggedMontage GetTaggedMontageByTag(const FGameplayTag& Tag);

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Montage")
	UAnimMontage* GetHitReactMontage() const;

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Summon")
	int32 GetMinionCount();

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Summon")
	void IncrementMinionCount(int32 NumToIncrement);
};
