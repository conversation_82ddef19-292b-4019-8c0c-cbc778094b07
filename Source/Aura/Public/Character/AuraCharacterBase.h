// Copyright iYuuki Studio

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "AbilitySystemInterface.h"
#include "AbilitySystem/Data/AuraCharacterClassInfo.h"
#include "Interaction/CombatInterface.h"
#include "AuraCharacterBase.generated.h"

class UDebuffNiagaraComponent;
class UGameplayAbility;
class UGameplayEffect;
class UAbilitySystemComponent;
class UAttributeSet;

UCLASS(Abstract)
class AURA_API AAuraCharacterBase : public ACharacter, public IAbilitySystemInterface, public ICombatInterface
{
	GENERATED_BODY()

public:
	AAuraCharacterBase();
	virtual UAbilitySystemComponent* GetAbilitySystemComponent() const override;
	UAttributeSet* GetAttributeSet() const { return AttributeSet; }

	virtual void Die(const FVector& DeathImpulse) override;

	UFUNCTION(NetMulticast, Reliable)
	virtual void MulticastHandleDeath(const FVector& DeathImpulse);

	/* Combat Interface */
	virtual UAnimMontage* GetHitReactMontage_Implementation() const override;
	virtual TArray<FTaggedMontage> GetAttackMontages_Implementation() override;
	virtual UNiagaraSystem* GetBloodEffect_Implementation() override;
	virtual FTaggedMontage GetTaggedMontageByTag_Implementation(const FGameplayTag& Tag) override;
	virtual int32 GetMinionCount_Implementation() override;
	virtual void IncrementMinionCount_Implementation(int32 NumToIncrement) override;
	virtual ECharacterClass GetCharacterClass_Implementation() override;
	virtual int32 GetCharacterLevel_Implementation() override;
	virtual FOnRegisteredASCDelegate GetOnRegisteredASCDelegate() override { return OnRegisteredASC; }
	virtual FOnDieDelegate GetOnDieDelegate() override { return OnDie; }
	virtual FOnDeathDelegate& GetOndeathDelegate() override { return OnDeath; }
	/* Combat Interface End */

	UPROPERTY(ReplicatedUsing=OnRep_Stunned, VisibleAnywhere, BlueprintReadOnly, Category = "Combat")
	bool bStunned = false;
	UPROPERTY(ReplicatedUsing=OnRep_Shocked, VisibleAnywhere, BlueprintReadOnly, Category = "Combat")
	bool bShocked = false;

	virtual void GetLifetimeReplicatedProps(TArray<class FLifetimeProperty>& OutLifetimeProps) const override;

protected:
	virtual void BeginPlay() override;
	virtual void InitAbilityActorInfo();
	virtual void InitialAttributes();
	virtual FVector GetCombatSocketLocation_Implementation(FGameplayTag AttackTag) override;
	virtual void AddCharacterAbilities();
	virtual void AddCharacterPassiveAbilities();
	virtual bool IsDead_Implementation() override;

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	TObjectPtr<USkeletalMeshComponent> Weapon;
	UPROPERTY(EditAnywhere)
	FName WeaponTipSocketName;
	UPROPERTY(EditAnywhere)
	FName LeftHandSocketName;
	UPROPERTY(EditAnywhere)
	FName RightHandSocketName;
	UPROPERTY(EditAnywhere)
	FName TailSocketName;

	UPROPERTY()
	TObjectPtr<UAbilitySystemComponent> AbilitySystemComponent;
	UPROPERTY()
	TObjectPtr<UAttributeSet> AttributeSet;
	UPROPERTY(EditAnywhere, Category = Attributes)
	TSubclassOf<UGameplayEffect> DefaultPrimaryAttributes;
	UPROPERTY(EditAnywhere, Category = Attributes)
	TSubclassOf<UGameplayEffect> DefaultSecondaryAttributes;
	UPROPERTY(EditAnywhere, Category = Attributes)
	TSubclassOf<UGameplayEffect> DefaultVitalAttributes;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Character Properties")
	int32 Level = 1;


	/* Dissolve Effects */
	UFUNCTION(NetMulticast, Reliable)
	void Dissolve();
	UFUNCTION(BlueprintImplementableEvent)
	void StartDissolveTimeLine(UMaterialInstanceDynamic* DynamicInst);
	UFUNCTION(BlueprintImplementableEvent)
	void StartWeaponDissolveTimeLine(UMaterialInstanceDynamic* DynamicInst);

	UPROPERTY(EditAnywhere, Category = "Combat|Dissolve")
	TObjectPtr<UMaterialInstance> DissolveMaterialInst;
	UPROPERTY(EditAnywhere, Category = "Combat|Dissolve")
	TObjectPtr<UMaterialInstance> WeaponDissolveMaterialInst;
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Summon")
	TArray<TSubclassOf<AActor>> SummonedMinionClasses;
	UPROPERTY(BlueprintReadWrite, Category = "Summon")
	int32 SpawnedMinionCount = 0;

	UPROPERTY(EditAnywhere, Category = "Character Defaults")
	ECharacterClass CharacterClass = ECharacterClass::Warrior;

	UPROPERTY(EditDefaultsOnly, Category = "Character Defaults")
	UDebuffNiagaraComponent* BurnNiagaraComponent;
	UPROPERTY(EditDefaultsOnly, Category = "Character Defaults")
	UDebuffNiagaraComponent* StunNiagaraComponent;

	FOnRegisteredASCDelegate OnRegisteredASC;
	FOnDieDelegate OnDie;
	FOnDeathDelegate OnDeath;

private:
	void ApplyAttributes(TSubclassOf<UGameplayEffect> Attributes);
	UFUNCTION()
	virtual void OnRep_Stunned();
	UFUNCTION()
	virtual void OnRep_Shocked();

	UPROPERTY(EditAnywhere, Category = "Ability")
	TArray<TSubclassOf<UGameplayAbility>> StartupAbilities;

	UPROPERTY(EditAnywhere, Category = "Ability")
	TArray<TSubclassOf<UGameplayAbility>> PassiveAbilities;

	UPROPERTY(EditAnywhere, Category = "Combat")
	TObjectPtr<UAnimMontage> HitReactMontage;

	UPROPERTY(EditDefaultsOnly, Category = "Montage")
	TArray<FTaggedMontage> AttackMontages;

	UPROPERTY(EditAnywhere, Category = "Combat")
	TObjectPtr<UNiagaraSystem> BloodEffect;

	UPROPERTY(EditAnywhere, Category = "Combat")
	TObjectPtr<USoundBase> DeathSound;
};
