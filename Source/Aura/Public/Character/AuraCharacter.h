// Copyright iYuuki Studio

#pragma once

#include "CoreMinimal.h"
#include "Character/AuraCharacterBase.h"
#include "Interaction/PlayerInterface.h"
#include "AuraCharacter.generated.h"

class UWidgetComponent;
class USpringArmComponent;
class <PERSON>ameraComponent;
class UNiagaraComponent;
/**
 * 
 */
UCLASS()
class AURA_API AAuraCharacter : public AAuraCharacterBase, public IPlayerInterface
{
	GENERATED_BODY()


public:
	AAuraCharacter();

	/* IPlayerInterface */
	virtual void AddToXp_Implementation(int32 XP) override;
	virtual void AddToLevel_Implementation(int32 InLevel) override;
	virtual void LevelUp_Implementation() override;
	virtual int32 GetXP_Implementation() override;
	virtual int32 GetLevelForXP_Implementation(int32 InXP) override;
	virtual int32 GetAttributePointsRewardBetweenLevels_Implementation(int32 InOldLevel, int32 InNewLevel) override;
	virtual int32 GetSpellPointsRewardBetweenLevels_Implementation(int32 InOldLevel, int32 InNewLevel) override;
	virtual void AddToAttributePoints_Implementation(int32 InAttributePoints) override;
	virtual void AddToSpellPoints_Implementation(int32 InSpellPoints) override;
	/* IPlayerInterface End */


protected:
	virtual void BeginPlay() override;
	virtual void PossessedBy(AController* NewController) override;
	virtual void OnRep_PlayerState() override;
	virtual void AddCharacterAbilities() override;

	virtual int32 GetCharacterLevel_Implementation() override;

private:
	virtual void InitAbilityActorInfo() override;

	UPROPERTY(EditAnywhere, Category = "Niagara")
	TObjectPtr<UNiagaraComponent> LevelUpNiagaraComponent;
	UPROPERTY(EditAnywhere, Category = "Camera")
	TObjectPtr<UCameraComponent> CameraComponent;
	UPROPERTY(EditAnywhere, Category = "Camera")
	TObjectPtr<USpringArmComponent> CameraBoom;


	UPROPERTY(EditAnywhere, Category = "LevelUp")
	TObjectPtr<USoundBase> LevelUpSound;
	UPROPERTY(EditAnywhere, Category = "LevelUp")
	TSubclassOf<UUserWidget> LevelUpWidgetClass;

	UFUNCTION(NetMulticast, reliable)
	void MulticastLevelUpParticles();

	UFUNCTION()
	void OnStunned();

	virtual void OnRep_Stunned() override;
};
