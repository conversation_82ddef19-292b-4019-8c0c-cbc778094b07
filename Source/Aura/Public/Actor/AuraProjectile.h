// Copyright iYuuki Studio

#pragma once

#include "CoreMinimal.h"
#include "AuraAbilityTypes.h"
#include "GameplayEffectTypes.h"
#include "GameFramework/Actor.h"
#include "AuraProjectile.generated.h"

class UNiagaraSystem;
class UProjectileMovementComponent;
class USphereComponent;

UCLASS()
class AURA_API AAuraProjectile : public AActor
{
	GENERATED_BODY()

public:
	AAuraProjectile();

	void SetDamageEffectParams(const FDamageEffectParams& EffectParams){DamageEffectParams = EffectParams;};

	UProjectileMovementComponent* GetProjectileMovement() const {return ProjectileMovement;}
protected:
	virtual void BeginPlay() override;
	virtual void Destroyed() override;

	UFUNCTION(BlueprintCallable, Category = "Projectile")
	void OnSphereOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

	UPROPERTY(VisibleAnywhere, Category = "Components", blueprintReadWrite)
	UProjectileMovementComponent* ProjectileMovement;
	
private:
	UPROPERTY(VisibleAnywhere)
	USphereComponent* Sphere;

	UPROPERTY(EditAnywhere)
	TObjectPtr<UNiagaraSystem> ImpactEffect;

	UPROPERTY(EditAnywhere)
	TObjectPtr<USoundBase> ImpactSound;

	UPROPERTY(EditAnywhere)
	TObjectPtr<USoundBase> LoopingSound;

	UPROPERTY()
	TObjectPtr<UAudioComponent> LoopingAudioComponent;

	UPROPERTY()
	bool bHit = false;
	UPROPERTY(EditDefaultsOnly)
	float LifeSpan = 15.f;

	bool bPlayedImpact = false;

	UPROPERTY()
	FDamageEffectParams DamageEffectParams;
};
