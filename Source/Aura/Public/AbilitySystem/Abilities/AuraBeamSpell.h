// Copyright iYuuki Studio

#pragma once

#include "CoreMinimal.h"
#include "AbilitySystem/Abilities/AuraDamageGameplayAbility.h"
#include "AuraBeamSpell.generated.h"

/**
 * 
 */
UCLASS()
class AURA_API UAuraBeamSpell : public UAuraDamageGameplayAbility
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, Category = "Cursor")
	void StoreCursorHit(const FHitResult& HitResult);

	UFUNCTION(BlueprintCallable, Category = "PlayerController")
	void StoreOwnerVariables();

	UFUNCTION(BlueprintCallable, Category = "Beam")
	void TraceFirstBlockingHit(const FVector& TargetLocation);

	UFUNCTION(BlueprintCallable, Category = "Beam")
	TArray<AActor*> DiffuseBeamToClosestActors( AActor* TargetActor);
protected:
	UPROPERTY(BlueprintReadOnly, Category = "Cursor")
	FHitResult StoredCursorHit;
	UPROPERTY(BlueprintReadOnly, Category = "Player")
	APlayerController* OwnerPlayerController;
	UPROPERTY(BlueprintReadOnly, Category = "Player")
	ACharacter* OwnerCharacter;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Beam")
	int32 MaxDiffusedTargets = 3;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Beam")
	float DiffusionRadius = 150.f;

	UFUNCTION(BlueprintImplementableEvent, Category = "Beam")
	void OnPrimaryTargetDied(AActor* DeathTarget);
	UFUNCTION(BlueprintImplementableEvent, Category = "Beam")
	void OnAdditionalTargetDied(AActor* DeathTarget);

	UFUNCTION(BlueprintCallable, Category = "Beam")
	void RemoveDelegates();
private:
	UPROPERTY()
	TArray<AActor*> BoundDeathTargets;
	
};
