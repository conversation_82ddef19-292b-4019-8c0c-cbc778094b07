// Copyright iYuuki Studio

#pragma once

#include "CoreMinimal.h"
#include "AuraAbilityTypes.h"
#include "AbilitySystem/Abilities/AuraGameplayAbility.h"
#include "AuraDamageGameplayAbility.generated.h"

/**
 * 
 */
UCLASS()
class AURA_API UAuraDamageGameplayAbility : public UAuraGameplayAbility
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, Category = Damage)
	void CauseDamage(AActor* TargetActor);
	UFUNCTION(BlueprintCallable, Category = Damage)
	void CauseDamageWithoutDebuff(AActor* TargetActor);
	UFUNCTION(BlueprintCallable, Category = Damage)
	void CauseDebuff(AActor* TargetActor);

	UFUNCTION(BlueprintCallable, Category = Damage)
	FDamageEffectParams MakeDamageEffectParamsFromClassDefaults(AActor* TargetActor = nullptr) const;

protected:
	UPROPERTY(EditAnywhere, Category = Damage)
	FGameplayTag DamageType;

	UPROPERTY(EditAnywhere, Category = Damage)
	FScalableFloat Damage;

	UPROPERTY(EditAnywhere, Category = Damage)
	float DeathImpulseMagnitude = 6000.f;

	UPROPERTY(EditAnywhere, Category = Damage)
	float KnockbackMagnitude = 500.f;
	UPROPERTY(EditAnywhere, Category = Damage)
	float KnockbackChance = 20.f;

	UPROPERTY(EditAnywhere, Category = Damage)
	float DebuffDamage = 5.f;
	UPROPERTY(EditAnywhere, Category = Damage)
	float DebuffDuration = 5.f;
	UPROPERTY(EditAnywhere, Category = Damage)
	float DebuffFrequency = 1.f;
	UPROPERTY(EditAnywhere, Category = Damage)
	float DebuffChance = 20.f;

	UPROPERTY(EditDefaultsOnly, Category = Damage)
	TSubclassOf<UGameplayEffect> DamageEffectClass;
};
